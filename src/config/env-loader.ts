import dotenv from "dotenv";
import path from "path";

/**
 * Load environment variables based on NODE_ENV
 * Loads .env.development for development, .env.production for production
 */
export function loadEnvironment() {
  const nodeEnv = process.env.NODE_ENV || "development";
  
  let envFile: string;
  if (nodeEnv === "development") {
    envFile = ".env.development";
  } else if (nodeEnv === "production") {
    envFile = ".env.production";
  } else {
    // Fallback to development for any other environment
    envFile = ".env.development";
  }

  const envPath = path.resolve(process.cwd(), envFile);
  
  // Load the environment file
  const result = dotenv.config({ path: envPath });
  
  if (result.error) {
    console.warn(`⚠️ Warning: Could not load ${envFile}. Using default environment variables.`);
  } else {
    console.log(`✅ Loaded environment from ${envFile}`);
  }
  
  return result;
}
