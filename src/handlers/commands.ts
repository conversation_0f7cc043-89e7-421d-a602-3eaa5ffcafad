import { Context } from "telegraf";
import {
  createMainKeyboard,
  createMarketplaceInlineKeyboard,
} from "../utils/keyboards";
import { MESSAGES } from "../constants/messages";
import { HealthcheckService } from "../services/healthcheck";

export const handleStartCommand = (ctx: Context) => {
  // Check if there's a start parameter (for referral links)
  const startPayload =
    ctx.message && "text" in ctx.message
      ? ctx.message.text.split(" ")[1]
      : undefined;

  if (startPayload?.startsWith("ref_")) {
    // Extract referral ID from the payload
    const referralId = startPayload.replace("ref_", "");

    // Create a special welcome message for referred users
    const referralWelcome = `${MESSAGES.WELCOME}

🎉 Welcome! You were referred by a friend.
🚀 Open the marketplace to get started and your referrer will earn rewards!`;

    // Create keyboard with web app that includes referral parameter
    const webAppUrlWithReferral = `${
      process.env.NODE_ENV === "development"
        ? process.env.LOCAL_WEB_APP_URL
        : process.env.WEB_APP_URL
    }?referral_id=${referralId}`;

    // Create a custom keyboard for referred users
    const referralKeyboard = {
      keyboard: [
        [
          {
            text: "🎁 Open Marketplace (Referral)",
            web_app: { url: webAppUrlWithReferral },
          },
        ],
        [{ text: "🛒 My Buy Orders" }, { text: "💰 My Sell Orders" }],
        [{ text: "🔗 Get Referral Link" }],
        [{ text: "📞 Contact Support" }],
      ],
      resize_keyboard: true,
    };

    ctx.reply(referralWelcome, { reply_markup: referralKeyboard });
  } else {
    // Normal start command
    ctx.reply(MESSAGES.WELCOME, createMainKeyboard());
  }
};

export const handleHelpCommand = (ctx: Context) => {
  ctx.reply(MESSAGES.HELP, createMarketplaceInlineKeyboard());
};

export const handleHealthCommand = async (ctx: Context) => {
  try {
    const lastHealthcheck = await HealthcheckService.getLastHealthcheck();
    const isHealthy = await HealthcheckService.isHealthy();

    if (lastHealthcheck) {
      const healthStatus = isHealthy ? "✅ Healthy" : "⚠️ Unhealthy";
      const message = `${healthStatus}\n\nLast healthcheck: ${lastHealthcheck}`;
      ctx.reply(message);
    } else {
      ctx.reply("❌ No healthcheck data found");
    }
  } catch (error) {
    console.error("Error in health command:", error);
    ctx.reply("❌ Error checking health status");
  }
};
