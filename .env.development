# Development Environment Configuration
NODE_ENV=development

# Bot Configuration
BOT_TOKEN=7870296231:AAH4uVlMKmpRreydCXCe0AysQ9Jvyf8BEmY
WEB_APP_URL=https://marketplace-ui-git-dev-mihailorudenkomaingmailcoms-projects.vercel.app/
WEBHOOK_URL=https://nodejs-telegram-bot-129956987825.us-central1.run.app

# Server Configuration
PORT=3001
LOG_LEVEL=debug

# Firebase Configuration (marketplace-dev project)
FIREBASE_PROJECT_ID=marketplace-dev-76a4a
FIREBASE_REGION=us-central1
NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSyDUuD5c26xFAEmlJp62ALpIuH4XRVpBdTc
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=marketplace-dev-76a4a.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=marketplace-dev-76a4a
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=marketplace-dev-76a4a.firebasestorage.app
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=1064526164290
NEXT_PUBLIC_FIREBASE_APP_ID=1:1064526164290:web:0944d053ce02c9ebfe98db

# Redis Configuration
REDIS_HOST_LOCAL=**********
REDIS_PORT_LOCAL=6379

# Marketplace Configuration
NEXT_PUBLIC_MARKETPLACE_WALLET_ADDRESS=UQCWcm7v_aEFlLEUmAYuryEfpepeRcFVSVIimfmCYmxbE7gI