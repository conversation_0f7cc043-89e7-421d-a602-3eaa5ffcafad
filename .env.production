# Production Environment Configuration
NODE_ENV=production

# Bot Configuration
BOT_TOKEN=7843246274:AAGXzvzh2QEU8j8I6YU7735kO3f5lh0aZ_g
WEB_APP_URL=https://marketplace-ui-blush.vercel.app/
WEBHOOK_URL=https://nodejs-telegram-bot-129956987825.us-central1.run.app

# Server Configuration
PORT=3001
LOG_LEVEL=info

# Firebase Configuration (marketplace production project)
FIREBASE_PROJECT_ID=marketplace-362c0
FIREBASE_REGION=us-central1
NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSyAyYRplB2DNVcaXGA-9boGe8X3Zii0NwAU
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=marketplace-362c0.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=marketplace-362c0
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=marketplace-362c0.firebasestorage.app
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=129956987825
NEXT_PUBLIC_FIREBASE_APP_ID=1:129956987825:web:db2089884d7375ca7cf390

# Redis Configuration
REDIS_HOST_LOCAL=**********
REDIS_PORT_LOCAL=6379

# Marketplace Configuration
NEXT_PUBLIC_MARKETPLACE_WALLET_ADDRESS=UQCWcm7v_aEFlLEUmAYuryEfpepeRcFVSVIimfmCYmxbE7gI